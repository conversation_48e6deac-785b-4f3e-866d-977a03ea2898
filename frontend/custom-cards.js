/**
 * Custom Card System for Blackjack Game
 * Replaces cards.js with SVG-based card rendering and smooth animations
 */

class CustomCardSystem {
    constructor() {
        this.cardSize = { width: 75, height: 100 }; // Standardized size for all cards
        this.animationSpeed = 500;
        this.container = null;
        this.zIndexCounter = 1;

        // Card mapping for SVG files
        this.suitMap = {
            'h': 'hearts',
            'd': 'diamonds',
            'c': 'clubs',
            's': 'spades'
        };

        this.rankMap = {
            1: 'ace', 2: '2', 3: '3', 4: '4', 5: '5', 6: '6', 7: '7', 8: '8', 9: '9', 10: '10',
            11: 'jack', 12: 'queen', 13: 'king'
        };

        // Generate all cards
        this.allCards = this.generateAllCards();
    }

    init(options) {
        if (options) {
            if (options.cardSize) this.cardSize = options.cardSize;
            if (options.animationSpeed) this.animationSpeed = options.animationSpeed;
            if (options.table) this.container = document.querySelector(options.table);
        }

        if (!this.container) {
            this.container = document.body;
        }

        console.log('Custom card system initialized');
    }

    generateAllCards() {
        const cards = [];
        const suits = ['h', 'd', 'c', 's'];
        const ranks = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];

        for (const suit of suits) {
            for (const rank of ranks) {
                cards.push({
                    suit: suit,
                    rank: rank,
                    id: `${rank}_${suit}`,
                    element: null,
                    faceUp: false
                });
            }
        }

        return cards;
    }

    getCardSVGPath(card) {
        if (!card.faceUp) {
            return 'svg/back.svg';
        }

        const suitName = this.suitMap[card.suit];
        const rankName = this.rankMap[card.rank];
        return `svg/${rankName}_of_${suitName}.svg`;
    }

    createCardElement(card, x, y) {
        const cardEl = document.createElement('div');
        cardEl.className = 'custom-card';
        cardEl.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            z-index: ${this.zIndexCounter++};
            cursor: pointer;
            transition: all ${this.animationSpeed}ms ease-out;
            transform-style: preserve-3d;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            box-sizing: border-box;
        `;

        const cardInner = document.createElement('div');
        cardInner.className = 'card-inner';
        cardInner.style.cssText = `
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.6s ease-in-out;
            transform-style: preserve-3d;
        `;

        const cardFront = document.createElement('div');
        cardFront.className = 'card-front';
        cardFront.style.cssText = `
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        `;

        const cardBack = document.createElement('div');
        cardBack.className = 'card-back';
        cardBack.style.cssText = `
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform: rotateY(180deg);
            background: white;
            border-radius: 8px;
            overflow: hidden;
        `;

        // Load SVG content
        this.loadCardSVG(cardFront, card, true);
        this.loadCardSVG(cardBack, { faceUp: false }, false);

        cardInner.appendChild(cardFront);
        cardInner.appendChild(cardBack);
        cardEl.appendChild(cardInner);

        // Set initial flip state
        if (!card.faceUp) {
            cardInner.style.transform = 'rotateY(180deg)';
        }

        card.element = cardEl;
        card.inner = cardInner;

        return cardEl;
    }

    loadCardSVG(element, card, isFront) {
        const svgPath = this.getCardSVGPath(card);

        fetch(svgPath)
            .then(response => response.text())
            .then(svgContent => {
                element.innerHTML = svgContent;
                const svg = element.querySelector('svg');
                if (svg) {
                    svg.style.cssText = `
                        width: 100%;
                        height: 100%;
                        display: block;
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading SVG:', error);
                // Fallback to colored rectangle
                element.style.background = isFront ? '#fff' : '#1a472a';
                element.innerHTML = `<div style="padding: 10px; font-size: 12px; color: ${isFront ? '#000' : '#fff'}">${card.rank || 'BACK'}</div>`;
            });
    }

    flipCard(card, faceUp, delay = 0) {
        if (!card.element || !card.inner) return;

        setTimeout(() => {
            card.faceUp = faceUp;
            if (faceUp) {
                card.inner.style.transform = 'rotateY(0deg)';
            } else {
                card.inner.style.transform = 'rotateY(180deg)';
            }
        }, delay);
    }

    moveCard(card, x, y, duration = null, delay = 0, flipAfterMove = false) {
        if (!card.element) return;

        const animDuration = duration || this.animationSpeed;

        setTimeout(() => {
            card.element.style.transition = `all ${animDuration}ms ease-out`;
            card.element.style.left = `${x}px`;
            card.element.style.top = `${y}px`;
            card.element.style.zIndex = this.zIndexCounter++;

            // If we should flip after moving, do it after a short delay
            if (flipAfterMove && !card.faceUp) {
                setTimeout(() => {
                    this.flipCard(card, true);
                }, animDuration * 0.7); // Flip when 70% through the move
            }
        }, delay);
    }

    dealCard(card, fromX, fromY, toX, toY, flipWhenMoving = true) {
        if (!card.element) return;

        // Start the card face down at the deck position
        card.faceUp = false;
        const element = this.createCardElement(card, fromX, fromY);
        this.container.appendChild(element);

        // Move and flip the card
        setTimeout(() => {
            this.moveCard(card, toX, toY, this.animationSpeed, 0, flipWhenMoving);
        }, 50); // Small delay to ensure element is rendered

        return card;
    }

    removeCard(card) {
        if (card.element && card.element.parentNode) {
            card.element.parentNode.removeChild(card.element);
            card.element = null;
            card.inner = null;
        }
    }
}

// Deck class for managing collections of cards
class CustomDeck {
    constructor(options = {}) {
        this.x = options.x || 0;
        this.y = options.y || 0;
        this.faceUp = options.faceUp || false;
        this.cards = [];
        this.cardSystem = window.customCardSystem;
    }

    addCard(card, animate = false) {
        card.faceUp = this.faceUp;
        this.cards.push(card);

        if (card.element) {
            if (animate) {
                // Animate the card moving to this deck position
                this.cardSystem.moveCard(card, this.x, this.y);
                // Flip to match deck's face state
                if (card.faceUp !== this.faceUp) {
                    setTimeout(() => {
                        this.cardSystem.flipCard(card, this.faceUp);
                    }, this.cardSystem.animationSpeed * 0.5);
                }
            } else {
                this.cardSystem.removeCard(card);
                const element = this.cardSystem.createCardElement(card, this.x, this.y);
                this.cardSystem.container.appendChild(element);
            }
        } else {
            const element = this.cardSystem.createCardElement(card, this.x, this.y);
            this.cardSystem.container.appendChild(element);
        }

        return this;
    }

    dealCardTo(targetDeck, flipWhenMoving = true) {
        if (this.cards.length === 0) return null;

        const card = this.pop();
        if (!card) return null;

        // Use the enhanced dealing animation
        this.cardSystem.dealCard(card, this.x, this.y, targetDeck.x, targetDeck.y, flipWhenMoving);

        // Add to target deck after animation
        setTimeout(() => {
            targetDeck.addCard(card, false); // Don't animate since it's already positioned
        }, this.cardSystem.animationSpeed);

        return card;
    }

    addCards(cards) {
        cards.forEach(card => this.addCard(card));
        return this;
    }

    pop() {
        return this.cards.pop();
    }

    get length() {
        return this.cards.length;
    }

    render(options = {}) {
        if (options.immediate) {
            // Immediate render without animation
            this.cards.forEach((card, index) => {
                if (card.element) {
                    card.element.style.transition = 'none';
                    card.element.style.left = `${this.x}px`;
                    card.element.style.top = `${this.y}px`;
                    card.element.style.zIndex = this.cardSystem.zIndexCounter + index;

                    // Reset transition after immediate positioning
                    setTimeout(() => {
                        card.element.style.transition = `all ${this.cardSystem.animationSpeed}ms ease-out`;
                    }, 10);
                }
            });
        } else {
            // Animated render
            this.cards.forEach((card, index) => {
                if (card.element) {
                    this.cardSystem.moveCard(card, this.x, this.y, null, index * 50);
                }
            });
        }
        return this;
    }
}

// Initialize global card system
window.customCardSystem = new CustomCardSystem();

// Export for compatibility with existing code
window.cards = {
    init: (options) => window.customCardSystem.init(options),
    all: window.customCardSystem.allCards,
    Deck: CustomDeck,
    Pile: CustomDeck // Pile is same as Deck for our purposes
};
